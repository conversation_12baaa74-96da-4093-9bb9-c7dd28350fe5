:is(.control, select, .select, input, textarea, .input, .textarea, .textinput, .control .input) {
  position: relative;

  width: 100%;
  box-shadow: none;
  color: var(--body-fg);
  background-color: transparent;
  border-color: var(--input-border);
  outline: 0;

  &::hover {
    border-color: var(--input-border) !important;
  }

  &:focus,
  &:active {
    box-shadow: none;
  }

  &::placeholder {
    color: var(--body-fg-a2);
  }

  .-required {
    color: var(--danger);
  }

  .-label {
    &:empty {
      display: none;
    }
  }

  .-input {
    min-height: 1.75rlh;
    font-weight: bold;
    outline: 0;
  }

  &:-webkit-autofill,
  &:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 50px var(--body-bg) inset;
    -webkit-text-fill-color: var(--body-fg);
  }

  .app-select .-label,
  .app-datetime .-label {
    margin-top: 0.3rem;
  }

  .app-shortcode .-label {
    margin-top: 0.6rem;
  }
}

.app-datetime {
  .date-time-control {
    position: relative;

    .input {
      width: calc(100% - 2em);
    }

    .button {
      position: absolute;
      right: 0;
      bottom: 0;

      background: transparent;
      border-left: 0;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}

.control {
  .control-icon {
    position: absolute;
    right: 0;
    bottom: 0;
  }

  [type="checkbox"] {
    width: auto;
  }
}

:is(select, .select) {
  color: var(--body-fg) !important;
  background: var(--body-bg) !important;
  border-color: var(--input-border) !important;
}

:is(input, textarea, .input, .textarea, .textinput, .control .input) {
  &:disabled {
    color: var(--body-fg);
    background: var(--body-bg);
    opacity: .5;
  }
}

.width-auto {
  width: auto !important;
}

.button {
  color: var(--body-fg);
  background-color: var(--body-bg);
  border-color: var(--input-border);

  &:hover {
    color: inherit;
    background-color: inherit;
  }

  &:disabled {
    color: var(--body-fg);
    background: var(--body-bg);
    opacity: .5;
  }

  &.is-text {
    color: var(--body-fg);
  }

  &.is-white {
    background: var(--body-bg);

    &:hover {
      background: color-mix(in oklch, var(--pure-white) 30%, transparent);
    }

    &:disabled {
      color: var(--body-fg);
      background: var(--body-bg);
      opacity: .5;
    }
  }

  &.is-static {
    color: var(--body-fg);
    background: var(--body-bg);
    border-color: var(--input-border);
    opacity: .5;
  }
}
