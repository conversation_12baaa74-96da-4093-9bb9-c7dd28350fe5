<template>
  <div class="_tag columns is-multiline">
    <div class="column is-5">
      <template v-if="props.availableTags.length && isInTagList">
        <app-select
          v-model="localTag.key"
          :options="props.availableTags"
          :id="`tag-key-${index}`"
          keyAlias="ShortCode"
          valueAlias="Name"
          :disabled="!canEditTag">
          Tag
        </app-select>
      </template>
      <template v-else>
        <app-text
          v-model="localTag.key"
          :id="`tag-key-${index}`"
          :disabled="true">
          Tag
        </app-text>
      </template>
    </div>
    <div class="column is-6">
      <component
        :is="getFormControl(localTag.key)"
        v-model="localTag.value"
        :id="`tag-value-${index}`"
        :options="getControlOptions(localTag.key)"
        :disabled="!canEditTag">
        Value
      </component>
    </div>
    <div class="_remove column is-1" @click="removeTag" :disabled="!canEditTag">
      <i class="far fa-trash-alt"></i>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import Access from '@/utils/access.js';

const props = defineProps({
  tag: { type: Object, required: true },
  index: { type: Number, required: true },
  availableTags: { type: Array, default: () => [] },
  canEditSystemTags: { type: Boolean, default: false },
  isNewlyAdded: { type: Boolean, default: false }
});
const emit = defineEmits(['remove-tag', 'update-tag']);

const localTag = ref({ ...props.tag });
watch(() => props.tag, newTag => localTag.value = { ...newTag }, { deep: true });

watch(
  localTag,
  (newTag) => {
    if (
      newTag.key !== props.tag.key ||
      newTag.value !== props.tag.value
    ) {
      emit('update-tag', props.index, { ...newTag });
    }
  },
  { deep: true }
);

const isInTagList = computed(() =>
  !localTag.value.key ||
  props.availableTags.some(t => t.ShortCode === localTag.value.key)
);

const isTagModifiable = computed(() => {
  if (props.isNewlyAdded) return true;

  const tag = props.availableTags.find(t => t.ShortCode === localTag.value.key);
  return tag ? tag.Modifiable : true;
});

const canEditTag = computed(() =>
  props.canEditSystemTags && isTagModifiable.value && isInTagList.value
);

const removeTag = () => {
  if (!canEditTag.value) return;
  emit('remove-tag', props.index);
}

const getFormControl = tagKey => {
  const target = props.availableTags.find(t => t.ShortCode === tagKey);
  return target?.Options?.length ? 'app-select-simple' : 'app-text';
};
const getControlOptions = tagKey =>
  props.availableTags.find(t => t.ShortCode === tagKey)?.Options ?? [];
</script>

<style scoped>
._remove {
  display: flex;
  justify-content: center;
  align-items: center;

  cursor: pointer;

  &:disabled {
    opacity: 0.5;
  }
}
</style>
