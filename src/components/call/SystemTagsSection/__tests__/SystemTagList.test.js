import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import SystemTagList from '../SystemTagList.vue';

// Mock the tops utility
vi.mock('@/utils/tops', () => ({
  default: {
    TOPSCompany: {
      GetSystemDataTags: vi.fn().mockResolvedValue({
        data: {
          Data: [
            { ShortCode: 'TAG1', Name: 'Tag 1', Options: [] },
            { ShortCode: 'TAG2', Name: 'Tag 2', Options: ['Option1', 'Option2'] }
          ]
        }
      })
    }
  }
}));

// Mock Access utility
vi.mock('@/utils/access.js', () => ({
  default: {
    has: vi.fn().mockReturnValue(true)
  }
}));

describe('SystemTagList', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = mount(SystemTagList, {
      props: {
        modelValue: ''
      },
      global: {
        stubs: {
          'app-grid-form': { template: '<div><slot /></div>' },
          'app-button': { template: '<button><slot /></button>' },
          'SystemTagItem': {
            template: '<div class="tag-item"></div>',
            props: ['tag', 'index', 'availableTags'],
            emits: ['remove-tag', 'update-tag']
          }
        }
      }
    });
  });

  it('should parse delimited string into internal array', async () => {
    await wrapper.setProps({ modelValue: 'key1=value1;key2=value2' });

    expect(wrapper.vm.internalTags.value).toEqual([
      { key: 'key1', value: 'value1' },
      { key: 'key2', value: 'value2' }
    ]);
  });

  it('should handle empty string', async () => {
    await wrapper.setProps({ modelValue: '' });

    expect(wrapper.vm.internalTags.value).toEqual([]);
  });

  it('should handle malformed strings gracefully', async () => {
    await wrapper.setProps({ modelValue: 'key1=value1;malformed;key2=value2' });

    expect(wrapper.vm.internalTags.value).toEqual([
      { key: 'key1', value: 'value1' },
      { key: 'malformed', value: '' },
      { key: 'key2', value: 'value2' }
    ]);
  });

  it('should handle empty values after equals sign', async () => {
    await wrapper.setProps({ modelValue: 'VEHICLEKEY=1511;=' });

    expect(wrapper.vm.internalTags.value).toEqual([
      { key: 'VEHICLEKEY', value: '1511' },
      { key: '', value: '' }
    ]);
  });

  it('should handle values containing equals signs', async () => {
    await wrapper.setProps({ modelValue: 'EQUATION=x=y+z;NORMAL=value' });

    expect(wrapper.vm.internalTags.value).toEqual([
      { key: 'EQUATION', value: 'x=y+z' },
      { key: 'NORMAL', value: 'value' }
    ]);
  });

  it('should handle the specific case: VEHICLEKEY=1511;=', async () => {
    await wrapper.setProps({ modelValue: 'VEHICLEKEY=1511;=' });

    // Both tags should be present in internalTags
    expect(wrapper.vm.internalTags.value).toHaveLength(2);
    expect(wrapper.vm.internalTags.value).toEqual([
      { key: 'VEHICLEKEY', value: '1511' },
      { key: '', value: '' }
    ]);
  });

  it('should emit flattened string when internal array changes', async () => {
    wrapper.vm.internalTags.value = [
      { key: 'newkey', value: 'newvalue' },
      { key: 'key2', value: 'value2' }
    ];

    await wrapper.vm.$nextTick();

    expect(wrapper.emitted('update:modelValue')).toBeTruthy();
    expect(wrapper.emitted('update:modelValue')[0][0]).toBe('newkey=newvalue;key2=value2');
  });

  it('should add new tag when addTag is called', async () => {
    // Access the exposed method
    wrapper.vm.addTag();

    // Should default to first available tag (TAG1 from mock)
    expect(wrapper.vm.internalTags.value).toContainEqual({ key: 'TAG1', value: '' });
  });

  it('should remove tag when removeTag is called', async () => {
    wrapper.vm.internalTags.value = [
      { key: 'key1', value: 'value1' },
      { key: 'key2', value: 'value2' }
    ];

    wrapper.vm.removeTag(0);

    expect(wrapper.vm.internalTags.value).toEqual([
      { key: 'key2', value: 'value2' }
    ]);
  });

  it('should update tag when updateTag is called', async () => {
    wrapper.vm.internalTags.value = [
      { key: 'key1', value: 'value1' }
    ];

    wrapper.vm.updateTag(0, { key: 'updatedkey', value: 'updatedvalue' });

    expect(wrapper.vm.internalTags.value[0]).toEqual({ key: 'updatedkey', value: 'updatedvalue' });
  });

  it('should filter out empty tags when flattening', async () => {
    wrapper.vm.internalTags.value = [
      { key: 'key1', value: 'value1' },
      { key: '', value: '' },
      { key: 'key2', value: 'value2' }
    ];

    await wrapper.vm.$nextTick();

    const emittedValue = wrapper.emitted('update:modelValue')[0][0];
    expect(emittedValue).toBe('key1=value1;key2=value2');
  });

  it('should default to first unused tag when adding', async () => {
    // Set up some existing tags
    wrapper.vm.internalTags.value = [
      { key: 'TAG1', value: 'existing' }
    ];

    // Add a new tag - should use TAG2 since TAG1 is already used
    wrapper.vm.addTag();

    expect(wrapper.vm.internalTags.value).toHaveLength(2);
    expect(wrapper.vm.internalTags.value[1]).toEqual({ key: 'TAG2', value: '' });
  });

  it('should disable add button when no unused tags available', async () => {
    // Set canEditSystemTags to true but use all available tags
    await wrapper.setProps({ canEditSystemTags: true });
    wrapper.vm.internalTags.value = [
      { key: 'TAG1', value: 'value1' },
      { key: 'TAG2', value: 'value2' }
    ];

    await wrapper.vm.$nextTick();

    expect(wrapper.vm.canAddTag.value).toBe(false);
  });

  it('should enable add button when unused tags are available', async () => {
    // Set canEditSystemTags to true and use only one tag
    await wrapper.setProps({ canEditSystemTags: true });
    wrapper.vm.internalTags.value = [
      { key: 'TAG1', value: 'value1' }
    ];

    await wrapper.vm.$nextTick();

    expect(wrapper.vm.canAddTag.value).toBe(true);
  });

  it('should track newly added tags', async () => {
    await wrapper.setProps({ canEditSystemTags: true });

    // Add a new tag
    wrapper.vm.addTag();

    // Should track the newly added tag
    expect(wrapper.vm.newlyAddedTags.value.includes('TAG1')).toBe(true);
  });

  it('should clear newly added flags when clearNewlyAddedFlags is called', async () => {
    await wrapper.setProps({ canEditSystemTags: true });

    // Add a new tag
    wrapper.vm.addTag();
    expect(wrapper.vm.newlyAddedTags.value.includes('TAG1')).toBe(true);

    // Clear the flags
    wrapper.vm.clearNewlyAddedFlags();
    expect(wrapper.vm.newlyAddedTags.value.length).toBe(0);
  });

  it('should clear newly added flags when AFTER_CALL_READ event is emitted', async () => {
    await wrapper.setProps({ canEditSystemTags: true });

    // Add a new tag
    wrapper.vm.addTag();
    expect(wrapper.vm.newlyAddedTags.value.includes('TAG1')).toBe(true);

    // Simulate the AFTER_CALL_READ event
    wrapper.vm.handleAfterCallRead();
    expect(wrapper.vm.newlyAddedTags.value.length).toBe(0);
  });
});
