import tops from '@/utils/tops';
import { commonTransforms, applyTransformSchema } from '@/utils/responseTransformers.js';

export default function useSystemTag (options) {
  const getAvailableSystemTags = async () => {
    try {
      const response = await tops.TOPSCompany.GetSystemDataTags();
      const transformedResponse = applyTransformSchema(response.data, commonTransforms.systemTags);
      return transformedResponse.Data;
    } catch {
      return [];
    }
  };

  return {
    getAvailableSystemTags
  };
}
